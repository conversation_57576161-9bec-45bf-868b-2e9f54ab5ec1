import 'dart:convert';

enum EventStatus {
  pending,
  confirmed,
  cancelled,
}

enum EventResponseStatus {
  pending, // User joined but hasn't confirmed final date
  suggested, // User suggested alternative dates
  confirmed, // User confirmed attendance for final date
  declined, // User declined to participate
}

enum EventTimeMode {
  specificTime,
  timeRange,
  fullDay,
  timeOfDay,
}

enum TimeOfDayPeriod {
  morning,
  afternoon,
  evening,
  midnight,
}

class EventHistoryEntry {
  final String action;
  final String? userId;
  final String? userName;
  final Map<String, dynamic>? details;
  final DateTime timestamp;

  EventHistoryEntry({
    required this.action,
    this.userId,
    this.userName,
    this.details,
    required this.timestamp,
  });

  factory EventHistoryEntry.fromJson(Map<String, dynamic> json) {
    return EventHistoryEntry(
      action: json['action'],
      userId: json['userId'],
      userName: json['userName'],
      details: json['details'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class CreateEventRequest {
  final String title;
  final String? description;
  final List<DateTime> eventDates;
  final String? eventTime;
  final String? location;
  final String? linkedTodoId;
  final String? linkedToGoId;
  final List<String>? tags;
  final Map<String, dynamic>? metadata;

  CreateEventRequest({
    required this.title,
    this.description,
    required this.eventDates,
    this.eventTime,
    this.location,
    this.linkedTodoId,
    this.linkedToGoId,
    this.tags,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      if (description != null) 'description': description,
      'eventDates': eventDates
          .map((date) => date.toIso8601String().split('T')[0])
          .toList(), // List of YYYY-MM-DD format
      if (eventTime != null) 'eventTime': eventTime,
      if (location != null) 'location': location,
      if (linkedTodoId != null) 'linkedTodoId': linkedTodoId,
      if (linkedToGoId != null) 'linkedToGoId': linkedToGoId,
      if (tags != null && tags!.isNotEmpty) 'tags': tags,
      if (metadata != null) 'metadata': metadata,
    };
  }
}

class EventModel {
  final String id;
  final String spaceId;
  final String messageId;
  final String creatorId;
  final String creatorName;
  final String title;
  final String? description;
  final List<DateTime> eventDates; // Changed from single eventDate to list
  final String? eventTime;
  final String? location;
  final String? linkedTodoId;
  final String? linkedToGoId;
  final List<String> tags;
  final String? confirmedDate;
  final String? confirmedTime;
  final EventStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<EventResponseModel> responses;
  final List<EventHistoryEntry> history;
  final Map<String, List<String>>
      userDateSelections; // { "2025-01-30": ["userId1", "userId2"] }

  EventModel({
    required this.id,
    required this.spaceId,
    required this.messageId,
    required this.creatorId,
    required this.creatorName,
    required this.title,
    this.description,
    required this.eventDates,
    this.eventTime,
    this.location,
    this.linkedTodoId,
    this.linkedToGoId,
    this.tags = const [],
    this.confirmedDate,
    this.confirmedTime,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.responses = const [],
    this.history = const [],
    this.userDateSelections = const {},
  });

  // Convenience getters
  bool get hasSuggestedDates => eventDates.isNotEmpty;

  DateTime? get firstEventDate =>
      eventDates.isNotEmpty ? eventDates.first : null;

  // Check if current user has selected any dates
  bool hasUserSelectedDates(String userId) {
    return userDateSelections.values.any((userIds) => userIds.contains(userId));
  }

  // Get dates selected by current user
  List<String> getUserSelectedDates(String userId) {
    final selectedDates = <String>[];
    userDateSelections.forEach((date, userIds) {
      if (userIds.contains(userId)) {
        selectedDates.add(date);
      }
    });
    return selectedDates;
  }

  factory EventModel.fromJson(Map<String, dynamic> json) {
    // Parse eventDates from JSON array
    List<DateTime> eventDates = [];
    if (json['eventDates'] != null) {
      eventDates = (json['eventDates'] as List<dynamic>)
          .map((dateStr) => DateTime.parse(dateStr))
          .toList();
    } else if (json['eventDate'] != null) {
      // Fallback for backward compatibility
      eventDates = [DateTime.parse(json['eventDate'])];
    }

    return EventModel(
      id: json['id'],
      spaceId: json['spaceId'],
      messageId: json['messageId'],
      creatorId: json['creatorId'],
      creatorName:
          json['creatorName'] ?? json['creator']?['displayName'] ?? 'Unknown',
      title: json['title'],
      description: json['description'],
      eventDates: eventDates,
      eventTime: json['eventTime'],
      location: json['location'],
      linkedTodoId: json['linkedTodoId'],
      linkedToGoId: json['linkedToGoId'],
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      confirmedDate: json['confirmedDate'],
      confirmedTime: json['confirmedTime'],
      status: _parseEventStatus(json['status']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      responses: _parseUserResponses(json['userResponses']),
      history: _parseHistory(json['history']),
      userDateSelections: _parseUserDateSelections(json['userDateSelections']),
    );
  }

  static List<EventResponseModel> _parseUserResponses(dynamic userResponses) {
    if (userResponses == null) return [];

    final Map<String, dynamic> responsesMap = userResponses is String
        ? Map<String, dynamic>.from(json.decode(userResponses))
        : Map<String, dynamic>.from(userResponses);

    return responsesMap.entries.map((entry) {
      final userId = entry.key;
      final responseData = Map<String, dynamic>.from(entry.value);
      return EventResponseModel.fromJson(userId, responseData);
    }).toList();
  }

  static EventStatus _parseEventStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return EventStatus.pending;
      case 'confirmed':
        return EventStatus.confirmed;
      case 'cancelled':
        return EventStatus.cancelled;
      default:
        return EventStatus.pending;
    }
  }

  static List<EventHistoryEntry> _parseHistory(dynamic history) {
    if (history == null) return [];

    final List<dynamic> historyList = history is String
        ? List<dynamic>.from(json.decode(history))
        : List<dynamic>.from(history);

    return historyList.map((entry) {
      return EventHistoryEntry.fromJson(Map<String, dynamic>.from(entry));
    }).toList();
  }

  static Map<String, List<String>> _parseUserDateSelections(
      dynamic userDateSelections) {
    if (userDateSelections == null) return {};

    final Map<String, dynamic> selectionsMap = userDateSelections is String
        ? Map<String, dynamic>.from(json.decode(userDateSelections))
        : Map<String, dynamic>.from(userDateSelections);

    return selectionsMap.map((date, userIds) {
      return MapEntry(date, List<String>.from(userIds));
    });
  }

  int get confirmedCount =>
      responses.where((r) => r.status == EventResponseStatus.confirmed).length;
  int get declinedCount =>
      responses.where((r) => r.status == EventResponseStatus.declined).length;
  int get suggestedCount =>
      responses.where((r) => r.status == EventResponseStatus.suggested).length;
  int get pendingCount =>
      responses.where((r) => r.status == EventResponseStatus.pending).length;
}

class EventResponseModel {
  final String userId;
  final String userName;
  final EventResponseStatus status;
  final String? note;
  final DateTime createdAt;
  final DateTime updatedAt;

  EventResponseModel({
    required this.userId,
    required this.userName,
    required this.status,
    this.note,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EventResponseModel.fromJson(String userId, Map<String, dynamic> json,
      {String? userName}) {
    return EventResponseModel(
      userId: userId,
      userName: userName ?? 'Unknown',
      status: _parseResponseStatus(json['status']),
      note: json['note'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  static EventResponseStatus _parseResponseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return EventResponseStatus.pending;
      case 'suggested':
        return EventResponseStatus.suggested;
      case 'confirmed':
        return EventResponseStatus.confirmed;
      case 'declined':
        return EventResponseStatus.declined;
      default:
        return EventResponseStatus.pending;
    }
  }
}

class JoinEventRequest {
  final EventResponseStatus status;
  final String? note;

  JoinEventRequest({
    required this.status,
    this.note,
  });

  Map<String, dynamic> toJson() {
    return {
      'status': status.name,
      if (note != null) 'note': note,
    };
  }
}

class SuggestEventDatesRequest {
  final List<String> suggestedDates; // YYYY-MM-DD format
  final String? note;

  SuggestEventDatesRequest({
    required this.suggestedDates,
    this.note,
  });

  Map<String, dynamic> toJson() {
    return {
      'suggestedDates': suggestedDates,
      if (note != null) 'note': note,
    };
  }
}
