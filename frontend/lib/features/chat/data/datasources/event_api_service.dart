import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/platform/platform_config.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../models/event_model.dart';

class EventApiService {
  final AuthService _authService;
  final PlatformConfig _platformConfig = PlatformConfig();

  EventApiService(this._authService);

  Future<Map<String, dynamic>> createEvent(
    String spaceId,
    CreateEventRequest request,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        return responseData['data'] ?? {};
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to create event');
      }
    } catch (e) {
      throw Exception('Failed to create event: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getEvents(
    String spaceId, {
    String? status,
    int? limit,
    int? offset,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final queryParams = <String, String>{};
      if (status != null) queryParams['status'] = status;
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final uri =
          Uri.parse('${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events')
              .replace(
                  queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final data = responseData['data'];
        if (data != null && data['events'] != null) {
          return List<Map<String, dynamic>>.from(data['events']);
        }
        return [];
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to get events');
      }
    } catch (e) {
      throw Exception('Failed to get events: $e');
    }
  }

  Future<Map<String, dynamic>> getEvent(
    String spaceId,
    String eventId,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.get(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return responseData['data'] ?? {};
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to get event');
      }
    } catch (e) {
      throw Exception('Failed to get event: $e');
    }
  }

  Future<Map<String, dynamic>> updateEvent(
    String spaceId,
    String eventId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.patch(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(updates),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return responseData['data'] ?? {};
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update event');
      }
    } catch (e) {
      throw Exception('Failed to update event: $e');
    }
  }

  Future<void> deleteEvent(
    String spaceId,
    String eventId,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.delete(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to delete event');
      }
    } catch (e) {
      throw Exception('Failed to delete event: $e');
    }
  }

  Future<Map<String, dynamic>> respondToEvent(
    String spaceId,
    String eventId,
    String response, // 'accepted' or 'rejected'
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final httpResponse = await http.post(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/responses'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'response': response}),
      );

      if (httpResponse.statusCode == 200 || httpResponse.statusCode == 201) {
        final responseData = jsonDecode(httpResponse.body);
        return responseData['data'] ?? {};
      } else {
        final errorData = jsonDecode(httpResponse.body);
        throw Exception(errorData['message'] ?? 'Failed to respond to event');
      }
    } catch (e) {
      throw Exception('Failed to respond to event: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getEventResponses(
    String spaceId,
    String eventId,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.get(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/responses'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(responseData['data'] ?? []);
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(
            errorData['message'] ?? 'Failed to get event responses');
      }
    } catch (e) {
      throw Exception('Failed to get event responses: $e');
    }
  }

  Future<Map<String, dynamic>> joinEvent(
    String spaceId,
    String eventId,
    JoinEventRequest request,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/join'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        return responseData['data'] ?? {};
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to join event');
      }
    } catch (e) {
      throw Exception('Failed to join event: $e');
    }
  }

  Future<void> suggestEventDates(
    String spaceId,
    String eventId,
    SuggestEventDatesRequest request,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/suggest-dates'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        final errorData = jsonDecode(response.body);
        throw Exception(
            errorData['message'] ?? 'Failed to suggest event dates');
      }
    } catch (e) {
      throw Exception('Failed to suggest event dates: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getEventParticipants(
    String spaceId,
    String eventId,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.get(
        Uri.parse(
            '${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/participants'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return (responseData['data'] as List).cast<Map<String, dynamic>>();
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(
            errorData['message'] ?? 'Failed to get event participants');
      }
    } catch (e) {
      throw Exception('Failed to get event participants: $e');
    }
  }
}
