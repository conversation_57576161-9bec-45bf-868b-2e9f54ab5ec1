import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../data/models/event_model.dart';
import '../../data/datasources/event_api_service.dart';
import '../dialogs/suggest_dates_dialog.dart';

class EventActivityTab extends StatefulWidget {
  final String spaceId;

  const EventActivityTab({
    super.key,
    required this.spaceId,
  });

  @override
  State<EventActivityTab> createState() => _EventActivityTabState();
}

class _EventActivityTabState extends State<EventActivityTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late EventApiService _eventApiService;
  List<EventModel> _events = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _eventApiService = DependencyInjection.getIt<EventApiService>();
    _loadEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    setState(() => _isLoading = true);

    try {
      final eventsData = await _eventApiService.getEvents(widget.spaceId);
      final events = eventsData
          .map((eventData) => EventModel.fromJson(eventData))
          .toList();

      setState(() {
        _events = events;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading events: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildTabBar(),
        Expanded(
          child: _buildTabContent(),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: [
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.schedule, size: 16),
                const SizedBox(width: 4),
                Text('Upcoming (${_getUpcomingEvents().length})'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check_circle, size: 16),
                const SizedBox(width: 4),
                Text('Past (${_getPastEvents().length})'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.cancel, size: 16),
                const SizedBox(width: 4),
                Text('Cancelled (${_getCancelledEvents().length})'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildEventsList(_getUpcomingEvents()),
        _buildEventsList(_getPastEvents()),
        _buildEventsList(_getCancelledEvents()),
      ],
    );
  }

  Widget _buildEventsList(List<EventModel> events) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (events.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: events.length,
        itemBuilder: (context, index) {
          final event = events[index];
          return _buildEventCard(event);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.6,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.event_busy,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No events found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Events will appear here when created',
                  style: TextStyle(
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Pull down to refresh',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEventCard(EventModel event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: () => _showEventParticipation(event),
        onLongPress: () => _showEventOptions(event),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.border,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEventHeader(event),
              const SizedBox(height: 8),
              _buildEventTitle(event),
              if (event.description != null &&
                  event.description!.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventDescription(event),
              ],
              if (event.hasSuggestedDates) ...[
                const SizedBox(height: 6),
                _buildEventDates(event),
              ],
              if (event.eventTime != null && event.eventTime!.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventTime(event),
              ],
              if (event.location != null && event.location!.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventLocation(event),
              ],
              const SizedBox(height: 6),
              _buildEventParticipantsInfo(event),
              if (event.tags.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventTags(event),
              ],
              const SizedBox(height: 8),
              _buildActionButtons(event),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventHeader(EventModel event) {
    return Row(
      children: [
        const Icon(
          Icons.event,
          color: AppColors.primary,
          size: 16,
        ),
        const SizedBox(width: 6),
        const Text(
          'Event',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        const SizedBox(width: 8),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getEventStatusColor(event.status).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getEventStatusText(event.status),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getEventStatusColor(event.status),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventTitle(EventModel event) {
    return Text(
      event.title,
      style: const TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 15,
      ),
    );
  }

  Widget _buildEventDescription(EventModel event) {
    return Text(
      event.description!,
      style: const TextStyle(
        fontSize: 13,
        color: AppColors.textSecondary,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildEventDates(EventModel event) {
    if (!event.hasSuggestedDates) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        const Icon(
          Icons.calendar_today,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            _formatDates(event.eventDates),
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventTime(EventModel event) {
    return Row(
      children: [
        const Icon(
          Icons.access_time,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          event.eventTime!,
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildEventLocation(EventModel event) {
    return Row(
      children: [
        const Icon(
          Icons.location_on,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            event.location!,
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildEventParticipantsInfo(EventModel event) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          Icons.people,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          '${event.responses.length} responses',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        const Spacer(),
        Text(
          'by ${event.creatorName}',
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildEventTags(EventModel event) {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: event.tags.map((tagName) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tagName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionButtons(EventModel event) {
    // Get current user ID
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Check if current user has selected dates
    final hasUserSelectedDates = event.hasUserSelectedDates(currentUserId);
    final suggestButtonText = hasUserSelectedDates ? 'Edit Suggest' : 'Suggest';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton.icon(
              onPressed: event.hasSuggestedDates
                  ? () => _handleJoinEvent(event)
                  : null,
              icon: Icon(
                Icons.person_add,
                size: 14,
                color:
                    event.hasSuggestedDates ? AppColors.primary : Colors.grey,
              ),
              label: Text(
                'Join',
                style: TextStyle(
                  color:
                      event.hasSuggestedDates ? AppColors.primary : Colors.grey,
                  fontSize: 12,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.grey[300],
            margin: const EdgeInsets.symmetric(horizontal: 6),
          ),
          Expanded(
            child: TextButton.icon(
              onPressed: () => _handleSuggestDates(event),
              icon: Icon(
                Icons.calendar_month,
                size: 14,
                color: Colors.grey[600],
              ),
              label: Text(
                suggestButtonText,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getEventStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.pending:
        return Colors.orange;
      case EventStatus.confirmed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.red;
    }
  }

  String _getEventStatusText(EventStatus status) {
    switch (status) {
      case EventStatus.pending:
        return 'Pending';
      case EventStatus.confirmed:
        return 'Confirmed';
      case EventStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _formatDates(List<DateTime> dates) {
    if (dates.isEmpty) return 'No dates';
    if (dates.length == 1) {
      return _formatSingleDate(dates.first);
    }
    return '${dates.length} suggested dates';
  }

  String _formatSingleDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return DateFormat('MMM d').format(date);
    }
  }

  void _showEventParticipation(EventModel event) {
    showDialog(
      context: context,
      builder: (context) => _EventParticipationDialog(
        event: event,
        eventApiService: _eventApiService,
        spaceId: widget.spaceId,
      ),
    );
  }

  void _showEventOptions(EventModel event) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit, color: AppColors.primary),
              title: const Text('Edit Event'),
              onTap: () {
                Navigator.pop(context);
                _showEditEventDialog(event);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: AppColors.error),
              title: const Text('Delete Event'),
              onTap: () {
                Navigator.pop(context);
                _showDeleteEventDialog(event);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleJoinEvent(EventModel event) async {
    try {
      final joinRequest = JoinEventRequest(
        status: EventResponseStatus.pending,
        note: null,
      );

      await _eventApiService.joinEvent(
        widget.spaceId,
        event.id,
        joinRequest,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully joined the event!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        // Refresh the events list
        _loadEvents();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to join event: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _handleSuggestDates(EventModel event) async {
    // Get current user ID and their previously selected dates
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Convert user's previously selected date strings to DateTime objects
    final userSelectedDateStrings = event.getUserSelectedDates(currentUserId);
    final initialSelectedDates = userSelectedDateStrings
        .map((dateStr) => DateTime.parse(dateStr))
        .toList();

    showDialog(
      context: context,
      builder: (dialogContext) => SuggestDatesDialog(
        event: event,
        initialSelectedDates: initialSelectedDates,
        onSuggestDates: (selectedDates) async {
          try {
            final suggestRequest = SuggestEventDatesRequest(
              suggestedDates: selectedDates
                  .map((date) =>
                      '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}')
                  .toList(),
              note: null,
            );

            await _eventApiService.suggestEventDates(
              widget.spaceId,
              event.id,
              suggestRequest,
            );

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Successfully suggested alternative dates!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
              // Refresh the events list
              _loadEvents();
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to suggest dates: $e'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showEditEventDialog(EventModel event) {
    // TODO: Implement edit event dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit event feature coming soon')),
    );
  }

  void _showDeleteEventDialog(EventModel event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEvent(event);
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteEvent(EventModel event) async {
    try {
      await _eventApiService.deleteEvent(widget.spaceId, event.id);
      await _loadEvents(); // Refresh the list
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Event deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting event: $e')),
        );
      }
    }
  }

  List<EventModel> _getUpcomingEvents() {
    final now = DateTime.now();
    return _events.where((event) {
      return event.status != EventStatus.cancelled &&
          (event.firstEventDate?.isAfter(now) ?? false);
    }).toList();
  }

  List<EventModel> _getPastEvents() {
    final now = DateTime.now();
    return _events.where((event) {
      return event.status != EventStatus.cancelled &&
          (event.firstEventDate?.isBefore(now) ?? false);
    }).toList();
  }

  List<EventModel> _getCancelledEvents() {
    return _events
        .where((event) => event.status == EventStatus.cancelled)
        .toList();
  }
}

class _EventParticipationDialog extends StatefulWidget {
  final EventModel event;
  final EventApiService eventApiService;
  final String spaceId;

  const _EventParticipationDialog({
    required this.event,
    required this.eventApiService,
    required this.spaceId,
  });

  @override
  State<_EventParticipationDialog> createState() =>
      _EventParticipationDialogState();
}

class _EventParticipationDialogState extends State<_EventParticipationDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildEventInfo(),
            const SizedBox(height: 16),
            _buildDateParticipation(),
            const SizedBox(height: 20),
            _buildCloseButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.people,
          color: AppColors.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        const Expanded(
          child: Text(
            'Event Participation',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.close),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  Widget _buildEventInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.event.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          if (widget.event.description != null &&
              widget.event.description!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              widget.event.description!,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.person,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                'Created by ${widget.event.creatorName}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateParticipation() {
    if (!widget.event.hasSuggestedDates) {
      return const Text(
        'No suggested dates for this event.',
        style: TextStyle(
          fontSize: 14,
          color: AppColors.textSecondary,
        ),
      );
    }

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Suggested Dates',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: widget.event.eventDates.length,
              itemBuilder: (context, index) {
                final date = widget.event.eventDates[index];
                return _buildDateCard(date);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateCard(DateTime date) {
    // TODO: Get actual participation data from API

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              DateFormat('EEEE, MMM d, yyyy').format(date),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '0 participants',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCloseButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => Navigator.pop(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Close',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
