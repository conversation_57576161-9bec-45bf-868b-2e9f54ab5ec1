import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../../data/models/message_model.dart';
import '../../data/models/event_model.dart';
import '../../data/datasources/event_api_service.dart';
import '../dialogs/suggest_dates_dialog.dart';

class EventMessageWidget extends StatefulWidget {
  final MessageModel message;
  final SpaceThemeProvider spaceTheme;

  const EventMessageWidget({
    super.key,
    required this.message,
    required this.spaceTheme,
  });

  @override
  State<EventMessageWidget> createState() => _EventMessageWidgetState();
}

class _EventMessageWidgetState extends State<EventMessageWidget> {
  EventModel? eventModel;
  bool _isDeleted = false;

  @override
  void initState() {
    super.initState();
    _parseEventData();
    // Check if message is deleted
    if (widget.message.isDeleted) {
      _isDeleted = true;
    }
  }

  @override
  void didUpdateWidget(EventMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.metadata != widget.message.metadata) {
      _parseEventData();
    }
  }

  void _parseEventData() {
    try {
      final metadata = widget.message.metadata;
      if (metadata != null) {
        eventModel = EventModel.fromJson(metadata);
      }
    } catch (e) {
      // If parsing fails, eventModel will remain null
      eventModel = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isDeleted) {
      return _buildDeletedMessage();
    }

    if (eventModel == null) {
      return _buildFallbackMessage();
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 320),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 8),
          _buildTitle(),
          if (eventModel!.description != null &&
              eventModel!.description!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildDescription(),
          ],
          if (eventModel!.hasSuggestedDates) ...[
            const SizedBox(height: 6),
            _buildDates(),
          ],
          if (eventModel!.eventTime != null &&
              eventModel!.eventTime!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildTime(),
          ],
          if (eventModel!.location != null &&
              eventModel!.location!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildLocation(),
          ],
          const SizedBox(height: 6),
          _buildParticipantsInfo(),
          if (eventModel!.tags.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildTags(),
          ],
          const SizedBox(height: 8),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.event,
          color: AppColors.primary,
          size: 16,
        ),
        const SizedBox(width: 6),
        const Text(
          'Event',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        const SizedBox(width: 8),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatusText(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitle() {
    return Text(
      eventModel!.title,
      style: const TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 15,
      ),
    );
  }

  Widget _buildDescription() {
    return Text(
      eventModel!.description!,
      style: const TextStyle(
        fontSize: 13,
        color: AppColors.textSecondary,
      ),
    );
  }

  Widget _buildDates() {
    if (!eventModel!.hasSuggestedDates) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        const Icon(
          Icons.calendar_today,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            _formatDates(eventModel!.eventDates),
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTime() {
    return Row(
      children: [
        const Icon(
          Icons.schedule,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          eventModel!.eventTime!,
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildLocation() {
    return Row(
      children: [
        const Icon(
          Icons.location_on,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            eventModel!.location!,
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildParticipantsInfo() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          Icons.people,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          '${eventModel!.responses.length} responses',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: eventModel!.tags.map((tagName) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tagName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  String _getStatusText() {
    switch (eventModel!.status) {
      case EventStatus.pending:
        return 'Pending';
      case EventStatus.confirmed:
        return 'Confirmed';
      case EventStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color _getStatusColor() {
    switch (eventModel!.status) {
      case EventStatus.pending:
        return Colors.orange;
      case EventStatus.confirmed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDates(List<DateTime> dates) {
    if (dates.isEmpty) return 'No dates';
    if (dates.length == 1) {
      return '${dates.first.day}/${dates.first.month}/${dates.first.year}';
    }
    return dates.map((date) => '${date.day}/${date.month}').join(', ');
  }

  Widget _buildFallbackMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event,
            color: Colors.grey[500],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.message.content.isNotEmpty
                  ? widget.message.content
                  : 'Event',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    // Get current user ID
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Check if current user has selected dates
    final hasUserSelectedDates =
        eventModel!.hasUserSelectedDates(currentUserId);
    final suggestButtonText = hasUserSelectedDates ? 'Edit Suggest' : 'Suggest';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton.icon(
              onPressed: eventModel!.hasSuggestedDates
                  ? () => _handleJoinEvent()
                  : null,
              icon: Icon(
                Icons.person_add,
                size: 14,
                color: eventModel!.hasSuggestedDates
                    ? AppColors.primary
                    : Colors.grey,
              ),
              label: Text(
                'Join',
                style: TextStyle(
                  color: eventModel!.hasSuggestedDates
                      ? AppColors.primary
                      : Colors.grey,
                  fontSize: 12,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.grey[300],
            margin: const EdgeInsets.symmetric(horizontal: 6),
          ),
          Expanded(
            child: TextButton.icon(
              onPressed: () => _handleSuggestDates(),
              icon: Icon(
                Icons.calendar_month,
                size: 14,
                color: Colors.grey[600],
              ),
              label: Text(
                suggestButtonText,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeletedMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_busy,
            color: Colors.grey[500],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Event was deleted',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  void _handleJoinEvent() async {
    if (widget.message.metadata == null) return;

    final eventModel = EventModel.fromJson(widget.message.metadata!);
    final eventApiService = DependencyInjection.getIt<EventApiService>();

    try {
      final joinRequest = JoinEventRequest(
        status: EventResponseStatus.pending,
        note: null,
      );

      await eventApiService.joinEvent(
        widget.message.spaceId,
        eventModel.id,
        joinRequest,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully joined the event!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to join event: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _handleSuggestDates() async {
    if (widget.message.metadata == null) return;

    final eventModel = EventModel.fromJson(widget.message.metadata!);
    final currentContext = context;

    // Get current user ID and their previously selected dates
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Convert user's previously selected date strings to DateTime objects
    final userSelectedDateStrings =
        eventModel.getUserSelectedDates(currentUserId);
    final initialSelectedDates = userSelectedDateStrings
        .map((dateStr) => DateTime.parse(dateStr))
        .toList();

    showDialog(
      context: currentContext,
      builder: (dialogContext) => SuggestDatesDialog(
        event: eventModel,
        initialSelectedDates: initialSelectedDates,
        onSuggestDates: (selectedDates) async {
          final eventApiService = DependencyInjection.getIt<EventApiService>();

          try {
            final suggestRequest = SuggestEventDatesRequest(
              suggestedDates: selectedDates
                  .map((date) =>
                      '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}')
                  .toList(),
              note: null,
            );

            await eventApiService.suggestEventDates(
              widget.message.spaceId,
              eventModel.id,
              suggestRequest,
            );

            if (mounted && currentContext.mounted) {
              ScaffoldMessenger.of(currentContext).showSnackBar(
                const SnackBar(
                  content: Text('Successfully suggested alternative dates!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          } catch (e) {
            if (mounted && currentContext.mounted) {
              ScaffoldMessenger.of(currentContext).showSnackBar(
                SnackBar(
                  content: Text('Failed to suggest dates: $e'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        },
      ),
    );
  }
}
