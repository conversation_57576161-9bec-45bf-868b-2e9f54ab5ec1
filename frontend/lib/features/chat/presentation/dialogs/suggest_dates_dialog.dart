import 'package:flutter/material.dart';
import '../../data/models/event_model.dart';
import '../widgets/calendar_picker_widget.dart';

class SuggestDatesDialog extends StatefulWidget {
  final EventModel event;
  final Function(List<DateTime> selectedDates) onSuggestDates;
  final List<DateTime>? initialSelectedDates;

  const SuggestDatesDialog({
    super.key,
    required this.event,
    required this.onSuggestDates,
    this.initialSelectedDates,
  });

  @override
  State<SuggestDatesDialog> createState() => _SuggestDatesDialogState();
}

class _SuggestDatesDialogState extends State<SuggestDatesDialog> {
  List<DateTime> _selectedDates = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Initialize with previously selected dates if provided
    if (widget.initialSelectedDates != null) {
      _selectedDates = List.from(widget.initialSelectedDates!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.event_available, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Suggest Alternative Dates',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Event title
            Text(
              'For: ${widget.event.title}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),

            // Calendar picker
            Expanded(
              child: CalendarPickerWidget(
                selectedDates: _selectedDates,
                onDatesChanged: (dates) {
                  setState(() {
                    _selectedDates = dates;
                  });
                },
              ),
            ),
            const SizedBox(height: 16),

            // Selected dates display
            if (_selectedDates.isNotEmpty) ...[
              Text(
                'Selected Dates:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: _selectedDates.map((date) {
                  return Chip(
                    label: Text(
                      '${date.day}/${date.month}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.blue[50],
                    side: BorderSide(color: Colors.blue[200]!),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading || _selectedDates.isEmpty
                        ? null
                        : _handleSuggestDates,
                    child: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Suggest Dates'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleSuggestDates() async {
    if (_selectedDates.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      widget.onSuggestDates(_selectedDates);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to suggest dates: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
