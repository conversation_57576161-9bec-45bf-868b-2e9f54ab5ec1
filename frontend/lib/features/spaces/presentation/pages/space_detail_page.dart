import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/utils/back_button_handler.dart';
import '../../../../core/utils/responsive_utils.dart';

import '../bloc/space_detail_bloc.dart';
import '../bloc/spaces_bloc.dart';
import '../../data/models/space_models.dart';
import '../widgets/space_theme_provider.dart';

import '../../../home/<USER>/bloc/timeline_bloc.dart';
import '../../../chat/presentation/widgets/chat_widget.dart';
import '../../../chat/presentation/bloc/chat_bloc.dart';
import '../../../chat/presentation/bloc/chat_event.dart';
import '../../../chat/presentation/widgets/todo_kanban_widget.dart';

import '../../../chat/presentation/bloc/chat_state.dart';
import '../../../chat/domain/services/unread_message_service.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../domain/services/online_members_service.dart';

import '../widgets/space_header.dart';
import '../../../balance/presentation/pages/balance_page.dart';
import '../../../balance/presentation/bloc/balance_bloc.dart';
import '../../../balance/data/services/balance_service.dart';
import '../../../chat/presentation/widgets/event_activity_tab.dart';
import '../../../togo/presentation/widgets/togo_tab_widget.dart';
import '../../../togo/presentation/bloc/togo_bloc.dart';
import '../../../tags/presentation/pages/tag_management_page.dart';
import '../../domain/usecases/clear_space_data_usecase.dart';
import '../widgets/space_responsive_timeline.dart';
import '../widgets/space_enhanced_calendar.dart';
import 'package:table_calendar/table_calendar.dart';

// 标签页配置类 - 便于扩展
class TabConfig {
  final IconData icon;
  final String text;
  final Widget Function(BuildContext context, SpaceDetailLoaded state) builder;
  final bool showBadge; // 是否显示未读消息提示
  final int Function(BuildContext context)? getBadgeCount; // 获取未读消息数量

  const TabConfig({
    required this.icon,
    required this.text,
    required this.builder,
    this.showBadge = false,
    this.getBadgeCount,
  });
}

class SpaceDetailPage extends StatefulWidget {
  final String spaceId;

  const SpaceDetailPage({super.key, required this.spaceId});

  @override
  State<SpaceDetailPage> createState() => _SpaceDetailPageState();
}

class _SpaceDetailPageState extends State<SpaceDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ChatBloc _chatBloc;

  // Calendar Tab 相關狀態
  DateTime _selectedDate = DateTime.now();
  DateTime _focusedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;
  bool _isTimelineView = true; // true for timeline, false for calendar

  // 調試按鈕位置
  Offset _debugButtonPosition = const Offset(20, 100); // 默認位置

  // 标签页配置列表 - 便于将来扩展
  List<TabConfig> get _tabConfigs => [
        TabConfig(
          icon: Icons.chat,
          text: 'Chat',
          builder: (context, state) => _buildChatTab(context, state),
          showBadge: true,
          getBadgeCount: (context) => _getUnreadMessageCount(context),
        ),
        TabConfig(
          icon: Icons.event,
          text: 'Event',
          builder: (context, state) => _buildEventTab(context, state),
        ),
        TabConfig(
          icon: Icons.calendar_month,
          text: 'Calendar',
          builder: (context, state) => _buildCalendarTab(context, state),
        ),
        TabConfig(
          icon: Icons.account_balance_wallet,
          text: 'Balance',
          builder: (context, state) => _buildBalanceTab(context, state),
        ),
        TabConfig(
          icon: Icons.checklist,
          text: 'TODOs',
          builder: (context, state) => _buildTodosTab(context, state),
        ),
        TabConfig(
          icon: Icons.map,
          text: 'Map',
          builder: (context, state) => _buildMapTab(context, state),
        ),
        TabConfig(
          icon: Icons.place,
          text: 'ToGo',
          builder: (context, state) => _buildToGoTab(context, state),
        ),
        TabConfig(
          icon: Icons.label,
          text: 'Tags',
          builder: (context, state) => _buildTagsTab(context, state),
        ),
      ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabConfigs.length, vsync: this);
    _chatBloc = DependencyInjection.getIt<ChatBloc>();

    // 延迟通知UnreadMessageService，避免在build过程中调用setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final unreadService = DependencyInjection.getIt<UnreadMessageService>();
      unreadService.setActiveSpace(widget.spaceId);
      unreadService.setActiveTabIndex(_tabController.index);

      // 标记当前用户为在线状态
      final onlineMembersService =
          DependencyInjection.getIt<OnlineMembersService>();
      final authService = DependencyInjection.getIt<AuthService>();
      final currentUser = authService.currentUser;
      if (currentUser != null) {
        debugPrint(
            'SpaceDetailPage: Marking user ${currentUser.id} as online in space ${widget.spaceId}');
        onlineMembersService.markUserOnline(widget.spaceId, currentUser.id);
      } else {
        debugPrint('SpaceDetailPage: No current user found');
      }

      // 加載所有 space 以支持左右切換
      _loadAllSpaces();
    });

    // 监听标签页切换，当切换到聊天标签页时清除未读消息提示
    _tabController.addListener(() {
      // 通知UnreadMessageService标签页变化
      final unreadService = DependencyInjection.getIt<UnreadMessageService>();
      unreadService.setActiveTabIndex(_tabController.index);

      if (_tabController.index == 0) {
        // Chat是第一个标签页
        // 当用户切换到聊天标签页时，标记为已读
        unreadService.markSpaceAsRead(widget.spaceId);
        setState(() {}); // 刷新UI以隐藏未读消息提示
      }
    });

    // 监听聊天状态变化以更新未读消息提示
    _chatBloc.stream.listen((state) {
      if (mounted) {
        setState(() {}); // 当聊天状态变化时刷新UI
      }
    });
  }

  @override
  void dispose() {
    // 清理UnreadMessageService的活跃状态
    final unreadService = DependencyInjection.getIt<UnreadMessageService>();
    unreadService.setActiveSpace(null);
    unreadService.setActiveTabIndex(-1);

    // 标记当前用户为离线状态
    final onlineMembersService =
        DependencyInjection.getIt<OnlineMembersService>();
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;
    if (currentUser != null) {
      onlineMembersService.markUserOffline(widget.spaceId, currentUser.id);
    }

    _tabController.dispose();
    super.dispose();
  }

  // 加載所有 space 以支持左右切換
  void _loadAllSpaces() {
    final spacesBloc = DependencyInjection.getIt<SpacesBloc>();

    spacesBloc.add(SpacesLoadRequested());
  }

  // 获取未读消息数量
  int _getUnreadMessageCount(BuildContext context) {
    try {
      final chatState = _chatBloc.state;

      if (chatState is ChatLoaded && chatState.spaceId == widget.spaceId) {
        // 计算未读消息数量 - 这里可以根据实际需求调整逻辑
        // 例如：计算当前用户未读的消息数量
        final authService = DependencyInjection.getIt<AuthService>();
        final currentUserId = authService.currentUser?.id;

        if (currentUserId != null) {
          final isCurrentTab = _tabController.index == 0; // Chat是第一个标签页

          // 如果当前就在聊天标签页，不显示未读提示
          if (isCurrentTab) {
            return 0;
          }

          // 计算其他用户发送的最近消息数量作为未读提示
          if (chatState.messages.isNotEmpty) {
            final now = DateTime.now();
            final recentMessages = chatState.messages.where((message) {
              // 只计算其他用户发送的消息
              final isFromOtherUser = message.senderId != currentUserId;
              // 只计算最近10分钟内的消息
              final isRecent =
                  now.difference(message.createdAt).inMinutes <= 10;
              return isFromOtherUser && isRecent;
            }).length;

            // 最多显示99
            return recentMessages > 99 ? 99 : recentMessages;
          }
        }
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  // 构建带有未读消息提示的标签页图标
  Widget _buildTabIconWithBadge(BuildContext context, TabConfig config) {
    final badgeCount = config.getBadgeCount?.call(context) ?? 0;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Icon(
          config.icon,
          size: 20,
        ),
        if (badgeCount > 0)
          Positioned(
            right: -6,
            top: -6,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.white, width: 1),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                badgeCount > 99 ? '99+' : badgeCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler.wrapWithBackHandler(
      context: context,
      isMainPage: false,
      pageName: 'SpaceDetailPage',
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => DependencyInjection.getIt<SpaceDetailBloc>()
              ..add(SpaceDetailLoadRequested(spaceId: widget.spaceId)),
          ),
          BlocProvider.value(
            value: DependencyInjection.getIt<TimelineBloc>(),
          ),
        ],
        child: BlocBuilder<SpaceDetailBloc, SpaceDetailState>(
          builder: (context, state) {
            return Stack(
              children: [
                Scaffold(
                  body: _buildScaffoldBody(state),
                ),
                // 可拖動的調試按鈕
                if (state is SpaceDetailLoaded &&
                    _shouldShowDebugFloatingActionButton(state))
                  _buildDraggableDebugButton(state),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildScaffoldBody(SpaceDetailState state) {
    if (state is SpaceDetailLoading) {
      return const Scaffold(
        appBar: null,
        body: Center(child: CircularProgressIndicator()),
      );
    } else if (state is SpaceDetailError) {
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading space',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.read<SpaceDetailBloc>().add(
                        SpaceDetailLoadRequested(
                          spaceId: widget.spaceId,
                        ),
                      );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    } else if (state is SpaceDetailLoaded) {
      return SpaceThemeProvider(
        space: state.space,
        child: Builder(
          builder: (context) {
            // 检查布局稳定性，避免在屏幕方向变化时渲染
            if (!ResponsiveUtils.isLayoutStable(context)) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            final spaceTheme = context.spaceTheme!;
            return Scaffold(
              appBar: AppBar(
                automaticallyImplyLeading: true, // 啟用返回按鈕和手勢
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => context.pop(),
                ),
                toolbarHeight: 120, // 增加高度以容纳空间信息
                // 使用默认主题色彩，与Home页面保持一致
                flexibleSpace: SafeArea(
                  child: Container(
                    padding: const EdgeInsets.only(
                      left: 56, // 為返回按鈕留出空間
                      right: AppConstants.defaultPadding,
                      top: AppConstants.defaultPadding,
                      bottom: AppConstants.defaultPadding,
                    ),
                    child: Row(
                      children: [
                        // Space Icon - 保留主题色
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: spaceTheme.primaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getSpaceIcon(state.space),
                            color: spaceTheme.onPrimaryColor,
                            size: 24,
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Space Info - 使用更新的SpaceHeader組件
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.surfaceVariant,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: SpaceHeader(space: state.space),
                          ),
                        ),

                        // Settings Button
                        IconButton(
                          icon: const Icon(Icons.settings),
                          onPressed: () {
                            context.push('/spaces/${widget.spaceId}/settings');
                          },
                          tooltip: 'Settings',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              body: Column(
                children: [
                  // Tab Bar - 响应式布局，iPad横版时优化显示
                  Container(
                    decoration: const BoxDecoration(
                      color: AppColors.surface,
                      border: Border(
                        bottom: BorderSide(
                          color: AppColors.border,
                          width: 1,
                        ),
                      ),
                    ),
                    height:
                        ResponsiveUtils.shouldUseIPadLandscapeLayout(context)
                            ? 80
                            : 72,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // iPad横版时使用更宽的标签页
                        final isIPadLandscape =
                            ResponsiveUtils.shouldUseIPadLandscapeLayout(
                                context);
                        final tabCount =
                            isIPadLandscape ? 5 : 4; // iPad横版显示5个标签
                        final tabWidth = constraints.maxWidth / tabCount;

                        return SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: tabWidth * _tabConfigs.length, // 动态计算总宽度
                            child: TabBar(
                              controller: _tabController,
                              labelColor: AppColors.primary,
                              unselectedLabelColor: AppColors.textSecondary,
                              indicatorColor: AppColors.primary,
                              isScrollable: false, // 关闭TabBar自带的滚动
                              tabAlignment: TabAlignment.fill,
                              padding: EdgeInsets.zero,
                              labelPadding: EdgeInsets.zero,
                              indicatorPadding: EdgeInsets.zero,
                              labelStyle: TextStyle(
                                fontSize: isIPadLandscape ? 12 : 11,
                              ),
                              unselectedLabelStyle: TextStyle(
                                fontSize: isIPadLandscape ? 12 : 11,
                              ),
                              tabs: _tabConfigs
                                  .map(
                                    (config) => SizedBox(
                                      width: tabWidth,
                                      child: Tab(
                                        icon: config.showBadge
                                            ? _buildTabIconWithBadge(
                                                context, config)
                                            : Icon(
                                                config.icon,
                                                size: 20,
                                              ),
                                        text: config.text,
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Tab Content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: _tabConfigs
                          .map((config) => config.builder(context, state))
                          .toList(),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );
    }

    return const Scaffold(
      body: Center(child: CircularProgressIndicator()),
    );
  }

  // Chat Tab - 聊天功能，类似WhatsApp
  Widget _buildChatTab(BuildContext context, SpaceDetailLoaded state) {
    return ChatWidget(spaceId: state.space.id);
  }

  // Event Tab - Event management
  Widget _buildEventTab(BuildContext context, SpaceDetailLoaded state) {
    return EventActivityTab(spaceId: state.space.id);
  }

  // Calendar Tab - Timeline 和 Calendar 視圖切換
  Widget _buildCalendarTab(BuildContext context, SpaceDetailLoaded state) {
    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: () async {
            // 刷新邏輯，將來可以添加數據刷新
            await Future.delayed(const Duration(milliseconds: 500));
          },
          child: _isTimelineView
              ? _buildTimelineView(context)
              : _buildCalendarView(context),
        ),
        // 視圖切換按鈕
        Positioned(
          bottom: 16,
          right: 16,
          child: FloatingActionButton(
            onPressed: () {
              setState(() {
                _isTimelineView = !_isTimelineView;
              });
            },
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            child: Icon(
              _isTimelineView ? Icons.calendar_month : Icons.timeline,
            ),
          ),
        ),
      ],
    );
  }

  /// Timeline 視圖
  Widget _buildTimelineView(BuildContext context) {
    return SpaceResponsiveTimeline(
      selectedDate: _selectedDate,
      onDateSelected: (DateTime date) {
        setState(() {
          _selectedDate = date;
        });
      },
    );
  }

  /// Calendar 視圖
  Widget _buildCalendarView(BuildContext context) {
    return Column(
      children: [
        // Calendar widget
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
          ),
          child: SpaceEnhancedCalendar(
            selectedDate: _selectedDate,
            focusedDay: _focusedDay,
            onDateSelected: (selectedDay) {
              setState(() {
                _selectedDate = selectedDay;
              });
            },
            onPageChanged: (focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
              });
            },
            calendarFormat: _calendarFormat,
            onFormatChanged: (format) {
              setState(() {
                _calendarFormat = format;
              });
            },
          ),
        ),
        const SizedBox(height: 16),
        // Selected date content area
        Expanded(
          child: _buildSelectedDateContent(context),
        ),
      ],
    );
  }

  /// 選中日期的內容區域
  Widget _buildSelectedDateContent(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.event_note,
              size: 48,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Selected Date',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Space events will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Balance Tab - 费用计算功能
  Widget _buildBalanceTab(BuildContext context, SpaceDetailLoaded state) {
    return BlocProvider(
      create: (context) => BalanceBloc(
        DependencyInjection.getIt<BalanceService>(),
      ),
      child: BalancePage(spaceId: widget.spaceId),
    );
  }

  // TODOs Tab - TODO 管理功能
  Widget _buildTodosTab(BuildContext context, SpaceDetailLoaded state) {
    return TodoKanbanWidget(
      spaceId: widget.spaceId,
      spaceName: state.space.name,
    );
  }

  // Map Tab - 地图功能
  Widget _buildMapTab(BuildContext context, SpaceDetailLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<SpaceDetailBloc>().add(
              SpaceDetailRefreshRequested(spaceId: widget.spaceId),
            );
      },
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.map, size: 64, color: AppColors.primary),
            ),
            const SizedBox(height: 24),
            Text(
              'Location & Map',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(
              'Share locations, find meeting points,\nand explore places together.',
              textAlign: TextAlign.center,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to map features
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Map feature coming soon!')),
                );
              },
              icon: const Icon(Icons.location_on),
              label: const Text('Share Location'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ToGo Tab - 想去的地方管理功能
  Widget _buildToGoTab(BuildContext context, SpaceDetailLoaded state) {
    return BlocProvider(
      create: (context) => DependencyInjection.getIt<ToGoBloc>(),
      child: ToGoTabWidget(spaceId: widget.spaceId),
    );
  }

  // Tags Tab - 標籤管理功能
  Widget _buildTagsTab(BuildContext context, SpaceDetailLoaded state) {
    return TagManagementPage(spaceId: widget.spaceId);
  }

  // 判断是否应该显示调试浮动按钮
  bool _shouldShowDebugFloatingActionButton(SpaceDetailLoaded state) {
    // 只在开发模式下显示（移除 owner 限制，因為還在開發階段）
    const bool isDebugMode = bool.fromEnvironment('dart.vm.product') == false;
    return isDebugMode;
  }

  // 構建可拖動的調試按鈕
  Widget _buildDraggableDebugButton(SpaceDetailLoaded state) {
    return Positioned(
      left: _debugButtonPosition.dx,
      top: _debugButtonPosition.dy,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            // 更新按鈕位置，確保不超出螢幕邊界
            final screenSize = MediaQuery.of(context).size;
            const buttonSize = 56.0; // FloatingActionButton 的默認大小

            double newX = _debugButtonPosition.dx + details.delta.dx;
            double newY = _debugButtonPosition.dy + details.delta.dy;

            // 限制在螢幕範圍內
            newX = newX.clamp(0.0, screenSize.width - buttonSize);
            newY = newY.clamp(0.0, screenSize.height - buttonSize);

            _debugButtonPosition = Offset(newX, newY);
          });
        },
        child: FloatingActionButton(
          heroTag: "draggable_debug",
          onPressed: () => _showDebugClearDataDialog(state),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          child: const Icon(Icons.delete_forever),
        ),
      ),
    );
  }

  // 显示调试清除数据对话框
  void _showDebugClearDataDialog(SpaceDetailLoaded state) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('⚠️ Debug Function'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'This will clear all data in this Space:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• All chat messages'),
              Text('• All todo items'),
              Text('• All togo places'),
              Text('• All events'),
              Text('• All event responses'),
              Text('• All tags'),
              Text('• All split records'),
              Text('• All balance records'),
              SizedBox(height: 16),
              Text(
                '⚠️ This action cannot be undone!',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearAllSpaceData(state);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Confirm Clear'),
            ),
          ],
        );
      },
    );
  }

  // 清除所有 Space 数据
  Future<void> _clearAllSpaceData(SpaceDetailLoaded state) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Clearing data...'),
              ],
            ),
          );
        },
      );

      // 调用清除数据 API
      final clearUseCase = DependencyInjection.getIt<ClearSpaceDataUseCase>();
      final result = await clearUseCase.execute(spaceId: state.space.id);

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '✅ Data cleared successfully! Deleted ${_formatDeletedCounts(result)} records'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
          ),
        );

        // 刷新页面数据
        context
            .read<ChatBloc>()
            .add(const ChatMessagesLoadRequested(isRefresh: true));
        context
            .read<SpaceDetailBloc>()
            .add(SpaceDetailLoadRequested(spaceId: state.space.id));
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                '❌ Failed to clear data: An error occurred during data clearing'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // 格式化删除计数
  String _formatDeletedCounts(Map<String, dynamic> result) {
    final deletedCounts =
        result['deletedCounts'] as Map<String, dynamic>? ?? {};
    final total = deletedCounts.values
        .fold<int>(0, (sum, count) => sum + (count as int? ?? 0));
    return total.toString();
  }

  IconData _getSpaceIcon(SpaceModel space) {
    // Personal spaces get a special icon
    if (space.isPersonal) {
      return Icons.person;
    }

    // For other spaces, use icon based on space name or default
    if (space.name.toLowerCase().contains('family')) {
      return Icons.family_restroom;
    } else if (space.name.toLowerCase().contains('work')) {
      return Icons.work;
    } else if (space.name.toLowerCase().contains('travel')) {
      return Icons.travel_explore;
    } else if (space.name.toLowerCase().contains('home')) {
      return Icons.home;
    } else {
      return Icons.space_dashboard;
    }
  }

  // Tab navigation methods
}
