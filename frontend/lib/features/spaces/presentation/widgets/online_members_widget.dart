import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/theme/app_colors.dart';
import '../../domain/services/online_members_service.dart';
import '../../data/models/space_member_model.dart';

/// 在线成员头像显示组件
/// 显示空间中的在线成员头像，支持折叠显示
class OnlineMembersWidget extends StatefulWidget {
  final String spaceId;
  final int maxVisibleMembers; // 最多显示的头像数量
  final double avatarSize; // 头像大小
  final double spacing; // 头像间距
  final bool showOnlineIndicator; // 是否显示在线指示器

  const OnlineMembersWidget({
    super.key,
    required this.spaceId,
    this.maxVisibleMembers = 5,
    this.avatarSize = 32,
    this.spacing = -8, // 负值表示重叠
    this.showOnlineIndicator = true,
  });

  @override
  State<OnlineMembersWidget> createState() => _OnlineMembersWidgetState();
}

class _OnlineMembersWidgetState extends State<OnlineMembersWidget> {
  late OnlineMembersService _onlineMembersService;
  List<SpaceMemberModel>? _cachedMembers;
  Set<String>? _cachedOnlineIds;
  Widget? _cachedWidget;

  @override
  void initState() {
    super.initState();
    _onlineMembersService = DependencyInjection.getIt<OnlineMembersService>();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _onlineMembersService,
      builder: (context, child) {
        final allMembers = _onlineMembersService.getAllMembers(widget.spaceId);
        final onlineMemberIds =
            _onlineMembersService.getOnlineMemberIds(widget.spaceId);

        // Check if we can use cached widget
        if (_cachedMembers != null &&
            _cachedOnlineIds != null &&
            _listEquals(_cachedMembers!, allMembers) &&
            _setEquals(_cachedOnlineIds!, onlineMemberIds) &&
            _cachedWidget != null) {
          return _cachedWidget!;
        }

        // Build new widget and cache it
        Widget newWidget;
        if (allMembers.isEmpty) {
          // 显示加载状态而不是完全隐藏
          newWidget = Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: const Text(
              'Loading members...',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          );
        } else {
          // 按最后上线时间排序，最近上线的在前面
          final sortedMembers =
              _sortMembersByLastOnline(allMembers, _onlineMembersService);
          newWidget = _buildMembersRow(context, sortedMembers, onlineMemberIds);
        }

        // Cache the result
        _cachedMembers = List.from(allMembers);
        _cachedOnlineIds = Set.from(onlineMemberIds);
        _cachedWidget = newWidget;

        return newWidget;
      },
    );
  }

  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  bool _setEquals<T>(Set<T> a, Set<T> b) {
    if (a.length != b.length) return false;
    return a.containsAll(b) && b.containsAll(a);
  }

  /// 按最后上线时间排序成员
  List<SpaceMemberModel> _sortMembersByLastOnline(
      List<SpaceMemberModel> members, OnlineMembersService service) {
    final sortedMembers = List<SpaceMemberModel>.from(members);

    sortedMembers.sort((a, b) {
      final aLastActive = service.getLastActiveTime(a.userId);
      final bLastActive = service.getLastActiveTime(b.userId);

      // 如果都没有活跃时间记录，按用户ID排序
      if (aLastActive == null && bLastActive == null) {
        return a.userId.compareTo(b.userId);
      }

      // 没有活跃时间的排在后面
      if (aLastActive == null) return 1;
      if (bLastActive == null) return -1;

      // 最近活跃的排在前面
      return bLastActive.compareTo(aLastActive);
    });

    return sortedMembers;
  }

  /// 构建成员行（包含在线和离线成员）
  Widget _buildMembersRow(BuildContext context, List<SpaceMemberModel> members,
      Set<String> onlineMemberIds) {
    final visibleMembers = members.take(widget.maxVisibleMembers).toList();
    final hiddenCount = members.length - visibleMembers.length;
    final onlineCount =
        members.where((m) => onlineMemberIds.contains(m.userId)).length;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 成员头像（重叠显示）
        SizedBox(
          width: visibleMembers.length * (widget.avatarSize + widget.spacing) -
              widget.spacing,
          height: widget.avatarSize,
          child: Stack(
            children: visibleMembers
                .asMap()
                .entries
                .map((entry) {
                  final index = entry.key;
                  final member = entry.value;
                  final isOnline = onlineMemberIds.contains(member.userId);

                  return Positioned(
                    left: index * (widget.avatarSize + widget.spacing),
                    child: _buildMemberAvatar(context, member, index,
                        isOnline: isOnline),
                  );
                })
                .toList()
                .reversed
                .toList(), // 反转顺序，让第一个头像在最上层
          ),
        ),

        // 显示更多成员数量
        if (hiddenCount > 0) ...[
          const SizedBox(width: 8),
          _buildMoreMembersIndicator(context, hiddenCount),
        ],

        // 成员状态文本 - 显示总成员数和在线数
        const SizedBox(width: 8),
        Text(
          onlineCount > 0
              ? '$onlineCount of ${members.length} online'
              : '${members.length} members',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildMemberAvatar(
      BuildContext context, SpaceMemberModel member, int index,
      {bool isOnline = true}) {
    return Container(
      width: widget.avatarSize,
      height: widget.avatarSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 头像（根据在线状态显示不同效果）
          ClipOval(
            child: ColorFiltered(
              colorFilter: isOnline
                  ? const ColorFilter.mode(
                      Colors.transparent, BlendMode.multiply)
                  : const ColorFilter.mode(Colors.grey, BlendMode.saturation),
              child: member.user.avatarUrl != null &&
                      member.user.avatarUrl!.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: member.user.avatarUrl!,
                      width: widget.avatarSize,
                      height: widget.avatarSize,
                      fit: BoxFit.cover,
                      placeholder: (context, url) =>
                          _buildAvatarPlaceholder(member, isOnline: isOnline),
                      errorWidget: (context, url, error) =>
                          _buildAvatarPlaceholder(member, isOnline: isOnline),
                    )
                  : _buildAvatarPlaceholder(member, isOnline: isOnline),
            ),
          ),

          // 在线指示器
          if (widget.showOnlineIndicator && isOnline)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 1.5,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAvatarPlaceholder(SpaceMemberModel member,
      {bool isOnline = true}) {
    // 根据用户名生成颜色
    final colors = [
      AppColors.primary,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    final colorIndex = member.user.displayName.hashCode % colors.length;
    final baseColor = colors[colorIndex.abs()];
    final backgroundColor = isOnline ? baseColor : Colors.grey;

    return Container(
      width: widget.avatarSize,
      height: widget.avatarSize,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          member.user.displayName.isNotEmpty
              ? member.user.displayName[0].toUpperCase()
              : '?',
          style: TextStyle(
            color: Colors.white,
            fontSize: widget.avatarSize * 0.4,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildMoreMembersIndicator(BuildContext context, int hiddenCount) {
    return Container(
      width: widget.avatarSize,
      height: widget.avatarSize,
      decoration: BoxDecoration(
        color: Colors.grey.shade600,
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          '+$hiddenCount',
          style: TextStyle(
            color: Colors.white,
            fontSize: widget.avatarSize * 0.3,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

/// 简化版在线成员指示器
/// 只显示在线数量，不显示头像
class OnlineMembersIndicator extends StatelessWidget {
  final String spaceId;
  final TextStyle? textStyle;

  const OnlineMembersIndicator({
    super.key,
    required this.spaceId,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: DependencyInjection.getIt<OnlineMembersService>(),
      builder: (context, child) {
        final onlineMembersService =
            DependencyInjection.getIt<OnlineMembersService>();
        final onlineCount = onlineMembersService.getOnlineMemberCount(spaceId);

        if (onlineCount == 0) {
          return const SizedBox.shrink();
        }

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: Color(0xFF10B981), // 使用更標準的綠色
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              '$onlineCount online',
              style: textStyle ??
                  Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(
                            0xFF64748B), // 使用AppColors.textSecondary的顏色
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
            ),
          ],
        );
      },
    );
  }
}

/// 在线成员详情弹窗
class OnlineMembersDialog extends StatelessWidget {
  final String spaceId;

  const OnlineMembersDialog({
    super.key,
    required this.spaceId,
  });

  static void show(BuildContext context, String spaceId) {
    showDialog(
      context: context,
      builder: (context) => OnlineMembersDialog(spaceId: spaceId),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: DependencyInjection.getIt<OnlineMembersService>(),
      builder: (context, child) {
        final onlineMembersService =
            DependencyInjection.getIt<OnlineMembersService>();
        final onlineMembers = onlineMembersService.getOnlineMembers(spaceId);

        return AlertDialog(
          title: Text('Online Members (${onlineMembers.length})'),
          content: SizedBox(
            width: double.maxFinite,
            child: onlineMembers.isEmpty
                ? const Text('No members are currently online.')
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: onlineMembers.length,
                    itemBuilder: (context, index) {
                      final member = onlineMembers[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundImage: member.user.avatarUrl != null &&
                                  member.user.avatarUrl!.isNotEmpty
                              ? CachedNetworkImageProvider(
                                  member.user.avatarUrl!)
                              : null,
                          child: member.user.avatarUrl == null ||
                                  member.user.avatarUrl!.isEmpty
                              ? Text(member.user.displayName.isNotEmpty
                                  ? member.user.displayName[0].toUpperCase()
                                  : '?')
                              : null,
                        ),
                        title: Text(member.user.displayName),
                        subtitle: Text(member.role.displayName),
                        trailing: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}
