import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../data/models/space_models.dart';
import '../../data/models/space_category_helper.dart';
import 'online_members_widget.dart';

class SpaceHeader extends StatelessWidget {
  final SpaceModel space;

  const SpaceHeader({
    super.key,
    required this.space,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Space Icon - 根據截圖設計
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: SpaceCategoryHelper.getEffectiveColor(
                space.category, space.color),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getSpaceIcon(),
            color: Colors.white,
            size: 24,
          ),
        ),

        const SizedBox(width: 12),

        // Space Info - 根據截圖布局優化
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Space名稱
              Text(
                space.name,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                      fontSize: 18,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              // 分類和成員數量在同一行 (根據截圖)
              Row(
                children: [
                  Text(
                    SpaceCategoryHelper.getDisplayName(space.category),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 14,
                        ),
                  ),
                  Text(
                    ' • ',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 14,
                        ),
                  ),
                  Text(
                    '${space.memberCount} members',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 14,
                        ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // 在線成員指示器 (根據截圖)
        OnlineMembersIndicator(
          spaceId: space.id,
          textStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
        ),
      ],
    );
  }

  IconData _getSpaceIcon() {
    // Personal spaces get a special icon
    if (space.isPersonal) {
      return Icons.person;
    }

    // Use category-based icon
    return SpaceCategoryHelper.getIcon(space.category);
  }
}
