# 📱 Space 前端UI元素完整文檔

## 📋 目錄
- [主要頁面](#主要頁面)
- [核心組件](#核心組件)
- [樣式系統](#樣式系統)
- [布局組件](#布局組件)
- [動畫效果](#動畫效果)
- [響應式設計](#響應式設計)

---

## 🏠 主要頁面

### 1. SpaceDetailPage (空間詳情頁)
**文件位置**: `frontend/lib/features/spaces/presentation/pages/space_detail_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar (120px height)               │
│ ┌─────┐ Space Name                  │
│ │ ←   │ Category • Members          │
│ └─────┘ Online Members Widget       │
├─────────────────────────────────────┤
│ TabBar (4 tabs)                     │
│ [Chat] [Posts] [Activity] [Calendar]│
├─────────────────────────────────────┤
│                                     │
│ TabBarView Content                  │
│ ┌─────────────────────────────────┐ │
│ │                                 │ │
│ │ Tab-specific content            │ │
│ │ (Chat/Posts/Activity/Calendar)  │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
└─────────────────────────────────────┘
```

#### AppBar詳細結構
- **Leading**: 返回按鈕 (56px寬度)
- **FlexibleSpace**:
  - 左邊距: 56px (為返回按鈕留空間)
  - 右邊距: 16px
  - 內容: SpaceHeader組件
- **工具欄高度**: 120px

#### Tab系統
- **Chat Tab**: ChatTabWidget (聊天界面)
- **Posts Tab**: PostsTabWidget (動態發布)
- **Activity Tab**: EventActivityTab (活動事件)
- **Calendar Tab**: SpaceEnhancedCalendar (日曆視圖)

#### 關鍵特性
- **SpaceThemeProvider**: 基於空間顏色的動態主題
- **響應式布局**: 支持移動端、平板、桌面端
- **調試功能**: 可拖動的調試按鈕（開發模式）

### 2. CreateSpacePage (創建空間頁)
**文件位置**: `frontend/lib/features/spaces/presentation/pages/create_space_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar                              │
│ "Create Space"                      │
├─────────────────────────────────────┤
│ SingleChildScrollView               │
│ ┌─────────────────────────────────┐ │
│ │ Header Section                  │ │
│ │ ┌─────┐                         │ │
│ │ │ 📦  │ Create New Space        │ │
│ │ └─────┘ Description text        │ │
│ ├─────────────────────────────────┤ │
│ │ Form Section                    │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Space Name TextField        │ │ │
│ │ └─────────────────────────────┘ │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Category Selector           │ │ │
│ │ │ [General ▼]                 │ │ │
│ │ └─────────────────────────────┘ │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Color Picker Grid           │ │ │
│ │ │ ●●●●●●●● (8 columns)        │ │ │
│ │ │ ●●●●●●●●                    │ │ │
│ │ │ ●●●●●●●●                    │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Action Section                  │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ [Create Space] Button       │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### Header區域 (64px圖標 + 文字)
- **圖標**: Icons.add_box, 64px, AppColors.primary
- **標題**: "Create New Space", 24px, FontWeight.bold
- **描述**: "Create a space to organize...", AppColors.textSecondary

#### 表單區域
- **空間名稱**: TextFormField, 圓角12px, 驗證必填
- **分類選擇**: 點擊觸發底部彈出層
- **顏色選擇**: 8列網格，24種顏色，圓形按鈕

#### 分類選擇器 (DraggableScrollableSheet)
```
┌─────────────────────────────────────┐
│ Handle Bar (40x4px)                 │
├─────────────────────────────────────┤
│ Header: Choose Space Category       │
├─────────────────────────────────────┤
│ ScrollView (10 categories)          │
│ ┌─────────────────────────────────┐ │
│ │ 🏠 General                      │ │
│ │ 👨‍👩‍👧‍👦 Family                       │ │
│ │ 👥 Friends                      │ │
│ │ ❤️ Couple                       │ │
│ │ 👥 Team                         │ │
│ │ 💼 Work                         │ │
│ │ 🎯 Hobby                        │ │
│ │ ✈️ Travel                       │ │
│ │ 🎓 Study                        │ │
│ │ 📋 Project                      │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 操作按鈕
- **創建按鈕**: 全寬度，圓角12px，帶加載狀態
- **加載狀態**: CircularProgressIndicator替換文字

### 3. SpaceSettingsPage (空間設置頁)
**文件位置**: `frontend/lib/features/spaces/presentation/pages/space_settings_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar                              │
│ "Space Settings"                    │
├─────────────────────────────────────┤
│ SingleChildScrollView               │
│ ┌─────────────────────────────────┐ │
│ │ Space Information Card          │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ 🏠 Space Name               │ │ │
│ │ │    Current Name             │ │ │
│ │ │ 📝 Description              │ │ │
│ │ │    Current Description      │ │ │
│ │ │ 🎨 Color & Category         │ │ │
│ │ │    Current Settings         │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Members Management Card         │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ 👥 View Members             │ │ │
│ │ │ 📧 Manage Invitations       │ │ │
│ │ │ 🔗 Invite Code Settings     │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Advanced Settings Card          │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ 🏷️ Tag Management            │ │ │
│ │ │ 🗂️ Clear Space Data          │ │ │
│ │ │ 📤 Leave Space              │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Danger Zone Card (Red)          │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ ⚠️ Delete Space             │ │ │
│ │ │   Permanently delete...     │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 設置分組詳細
1. **Space Information Card**
   - Space Name: ListTile + 編輯對話框
   - Description: ListTile + 編輯對話框
   - Color & Category: ListTile + 選擇器

2. **Members Management Card**
   - View Members: 導航到成員頁面
   - Manage Invitations: 導航到邀請頁面
   - Invite Code: 顯示/重新生成

3. **Advanced Settings Card**
   - Tag Management: 導航到標籤管理
   - Clear Data: 確認對話框
   - Leave Space: 確認對話框

4. **Danger Zone Card**
   - Delete Space: 僅Owner可見，紅色主題

#### 權限控制
- **PermissionListTile**: 基於權限的列表項
- **角色檢查**: Owner/Admin/Member權限區分
- **禁用狀態**: 無權限時顯示灰色並禁用點擊

### 4. SpaceInvitesPage (邀請管理頁)
**文件位置**: `frontend/lib/features/spaces/presentation/pages/space_invites_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar                              │
│ "Invitation Management" [+] [🔄]    │
├─────────────────────────────────────┤
│ RefreshIndicator                    │
│ ┌─────────────────────────────────┐ │
│ │ Space Invite Card               │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ 🔗 Permanent Invite Code    │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │
│ │ │ │ ABC123XYZ               │ │ │ │
│ │ │ │ [Copy] [Share]          │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │
│ │ │ QR Code (optional)          │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Quick Actions Card              │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ 👤 Invite User by Email     │ │ │
│ │ │ 🔄 Regenerate Invite Code   │ │ │
│ │ │ 📋 Copy Invite Link         │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Invite Settings Card            │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ ⚙️ Invitation Settings      │ │ │
│ │ │ • Auto-approve invites      │ │ │
│ │ │ • Require admin approval    │ │ │
│ │ │ • Maximum members limit     │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### AppBar操作
- **邀請用戶**: Icons.person_add, 觸發邀請對話框
- **重新生成**: Icons.refresh, 重新生成邀請碼

#### 功能區域詳細
1. **邀請碼卡片**
   - 永久邀請碼顯示
   - 複製和分享按鈕
   - QR碼生成（可選）

2. **快速操作**
   - 郵件邀請: 輸入郵箱地址
   - 重新生成: 確認對話框
   - 複製鏈接: 一鍵複製完整邀請鏈接

3. **邀請設置**
   - 自動批准開關
   - 管理員審核設置
   - 成員數量限制

### 5. SpaceMembersPage (成員管理頁)
**文件位置**: `frontend/lib/features/spaces/presentation/pages/space_members_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar                              │
│ "Members" [+]                       │
├─────────────────────────────────────┤
│ RefreshIndicator                    │
│ ┌─────────────────────────────────┐ │
│ │ ListView.builder                │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Member Card 1               │ │ │
│ │ │ ┌───┐ John Doe              │ │ │
│ │ │ │ 👤│ Owner                  │ │ │
│ │ │ └───┘ [⋮]                   │ │ │
│ │ ├─────────────────────────────┤ │ │
│ │ │ Member Card 2               │ │ │
│ │ │ ┌───┐ Jane Smith            │ │ │
│ │ │ │ 👤│ Admin                  │ │ │
│ │ │ └───┘ [⋮]                   │ │ │
│ │ ├─────────────────────────────┤ │ │
│ │ │ Member Card 3               │ │ │
│ │ │ ┌───┐ Bob Wilson            │ │ │
│ │ │ │ 👤│ Member                 │ │ │
│ │ │ └───┘ [⋮]                   │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Empty State (when no members):      │
│ ┌─────────────────────────────────┐ │
│ │           👥                    │ │
│ │        No members               │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 成員卡片結構
```
┌─────────────────────────────────────┐
│ ┌───┐ Display Name                  │
│ │ 👤│ Role Badge                    │
│ └───┘ Last seen: timestamp          │
│                              [⋮]    │
└─────────────────────────────────────┘
```

#### 成員卡片元素
- **頭像**: 40x40px圓形，MemberAvatar組件
- **姓名**: displayName, 16px, FontWeight.w600
- **角色**: Owner/Admin/Member徽章
- **最後在線**: 時間戳顯示
- **操作菜單**: 三點菜單（權限控制）

#### 操作菜單選項
- **提升為管理員**: 僅Owner可操作
- **降級為成員**: 僅Owner可操作
- **移除成員**: Owner/Admin可操作
- **查看資料**: 所有人可查看

#### 空狀態設計
- **圖標**: Icons.people_outline, 64px, 灰色
- **文字**: "No members", 18px, 灰色
- **居中顯示**: MainAxisAlignment.center

### 6. HomePage - Spaces Tab (主頁空間列表)
**文件位置**: `frontend/lib/features/home/<USER>/pages/home_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar                              │
│ "MeLo" [🔍] [👤]                    │
├─────────────────────────────────────┤
│ TabBar                              │
│ [Timeline] [Calendar] [Spaces]      │
├─────────────────────────────────────┤
│ Spaces Tab Content                  │
│ ┌─────────────────────────────────┐ │
│ │ ListView.builder                │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ StaggeredListAnimation      │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │
│ │ │ │ InteractiveListItem     │ │ │ │
│ │ │ │ ┌─────────────────────┐ │ │ │ │
│ │ │ │ │ SpaceCard           │ │ │ │ │
│ │ │ │ │ ┌──┐ Space Name     │ │ │ │ │
│ │ │ │ │ │🏠│ Category       │ │ │ │ │
│ │ │ │ │ └──┘ [5] unread     │ │ │ │ │
│ │ │ │ └─────────────────────┘ │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │
│ │ ├─────────────────────────────┤ │ │
│ │ │ (More SpaceCards...)        │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
│                                     │
│ FloatingActionButton                │
│ [+] Create Space                    │
└─────────────────────────────────────┘
```

#### Spaces列表特性
- **動畫效果**: StaggeredListAnimation，50ms延遲
- **交互反饋**: InteractiveListItem，觸覺反饋
- **滾動物理**: BouncingScrollPhysics
- **邊距**: 水平16px，垂直8px

#### 空間卡片在列表中的布局
- **間距**: 底部8px間距
- **點擊區域**: 整個卡片可點擊
- **邀請狀態**: pending狀態時禁用點擊
- **未讀消息**: 右側顯示未讀數量徽章

#### FloatingActionButton
- **位置**: 右下角
- **圖標**: Icons.add
- **操作**: 導航到CreateSpacePage
- **主題**: 使用AppColors.primary

### 7. JoinSpacePage (加入空間頁)
**文件位置**: `frontend/lib/features/spaces/presentation/pages/join_space_page.dart`

#### 頁面Layout結構
```
┌─────────────────────────────────────┐
│ AppBar                              │
│ "Join Space" [←]                    │
├─────────────────────────────────────┤
│ Form (Column)                       │
│ ┌─────────────────────────────────┐ │
│ │ Header Section                  │ │
│ │ ┌─────┐                         │ │
│ │ │ 👥  │ Enter Invite Code       │ │
│ │ └─────┘ Description text        │ │
│ ├─────────────────────────────────┤ │
│ │ Input Section                   │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ 🔑 Invite Code TextField    │ │ │
│ │ │    [Enter space invite...]  │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Status Section                  │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Loading Card (if loading)   │ │ │
│ │ │ ⏳ Verifying invite code... │ │ │
│ │ └─────────────────────────────┘ │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Error Card (if error)       │ │ │
│ │ │ ❌ Failed to join space...  │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Spacer                          │ │
│ ├─────────────────────────────────┤ │
│ │ Action Section                  │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ [Join Space] Button         │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### Header區域
- **圖標**: Icons.group_add, 64px, AppColors.primary
- **標題**: "Enter Invite Code", headlineMedium, FontWeight.bold
- **描述**: "Please enter the space invite code to join", bodyMedium, 灰色

#### 輸入區域
- **邀請碼輸入框**:
  - 前綴圖標: Icons.vpn_key
  - 標籤: "Invite Code"
  - 提示: "Enter space invite code"
  - 大寫轉換: TextCapitalization.characters
  - 驗證: 最少3個字符

#### 狀態卡片
1. **加載狀態卡片**
   - 圓形進度指示器 (20x20px)
   - 文字: "Verifying invite code..."

2. **錯誤狀態卡片**
   - 背景: AppColors.error.withValues(alpha: 0.1)
   - 圖標: Icons.error_outline, AppColors.error
   - 錯誤信息文字

#### 操作按鈕
- **加入按鈕**: 全寬度，垂直內邊距16px
- **加載狀態**: 顯示CircularProgressIndicator
- **禁用狀態**: 加載時禁用點擊

#### 導航行為
- **成功加入**: 導航到空間詳情頁
- **返回**: 導航到主頁
- **PopScope**: 禁用默認返回，強制導航到主頁

---

## 📱 Tab頁面詳細Layout

### 1. Chat Tab (聊天頁面)
**組件**: `ChatWidget`

#### Layout結構
```
┌─────────────────────────────────────┐
│ Column                              │
│ ┌─────────────────────────────────┐ │
│ │ Expanded - Chat Content         │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ RefreshIndicator            │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │
│ │ │ │ ListView.builder        │ │ │ │
│ │ │ │ ┌─────────────────────┐ │ │ │ │
│ │ │ │ │ MessageBubble 1     │ │ │ │ │
│ │ │ │ ├─────────────────────┤ │ │ │ │
│ │ │ │ │ MessageBubble 2     │ │ │ │ │
│ │ │ │ ├─────────────────────┤ │ │ │ │
│ │ │ │ │ MessageBubble 3     │ │ │ │ │
│ │ │ │ └─────────────────────┘ │ │ │ │
│ │ │ │ TypingIndicator         │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ ChatInput (Input Area)          │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Reply Preview (if replying) │ │ │
│ │ ├─────────────────────────────┤ │ │
│ │ │ TextField + Send Button     │ │ │
│ │ │ [📎] [Type message...] [📤] │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 聊天內容區域
- **消息列表**: ListView.builder，反向顯示
- **消息氣泡**: MessageBubble組件，支持文字、圖片、文件
- **下拉刷新**: RefreshIndicator加載歷史消息
- **輸入指示器**: TypingIndicator顯示正在輸入的用戶
- **回覆功能**: 支持回覆特定消息

#### 輸入區域
- **回覆預覽**: 顯示被回覆的消息預覽
- **文字輸入**: TextField支持多行輸入
- **附件按鈕**: 支持圖片、文件上傳
- **發送按鈕**: 發送文字或語音消息

### 2. Event Tab (活動頁面)
**組件**: `EventActivityTab`

#### Layout結構
```
┌─────────────────────────────────────┐
│ Center                              │
│ ┌─────────────────────────────────┐ │
│ │ Column (MainAxisAlignment.center)│ │
│ │ ┌─────┐                         │ │
│ │ │ 📅  │ No Events Yet           │ │
│ │ └─────┘ Space events will       │ │
│ │         appear here             │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 空狀態設計
- **圖標**: Icons.event, 64px, AppColors.textSecondary
- **標題**: "No Events Yet", headlineSmall
- **描述**: "Space events will appear here", bodyMedium
- **居中對齊**: MainAxisAlignment.center

### 3. Calendar Tab (日曆頁面)
**組件**: `SpaceEnhancedCalendar`

#### Layout結構
```
┌─────────────────────────────────────┐
│ Stack                               │
│ ┌─────────────────────────────────┐ │
│ │ RefreshIndicator                │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Timeline View / Calendar    │ │ │
│ │ │ ┌─────────────────────────┐ │ │ │
│ │ │ │ TableCalendar           │ │ │ │
│ │ │ │ ┌─────┬─────┬─────────┐ │ │ │ │
│ │ │ │ │ Mon │ Tue │ Wed ... │ │ │ │ │
│ │ │ │ ├─────┼─────┼─────────┤ │ │ │ │
│ │ │ │ │  1  │  2  │   3     │ │ │ │ │
│ │ │ │ │  8  │  9  │  10     │ │ │ │ │
│ │ │ │ └─────┴─────┴─────────┘ │ │ │ │
│ │ │ └─────────────────────────┘ │ │ │
│ │ └─────────────────────────────┘ │ │
│ ├─────────────────────────────────┤ │
│ │ Positioned (bottom: 16, right:16)│ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ FloatingActionButton        │ │ │
│ │ │ [📅] / [📈] Toggle View     │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 日曆功能
- **視圖切換**: Timeline視圖 ↔ Calendar視圖
- **日期選擇**: 點擊日期選擇
- **事件標記**: 有事件的日期顯示標記
- **手勢支持**: 滑動切換月份（可配置）
- **今日高亮**: 當前日期特殊顯示

#### 切換按鈕
- **位置**: 右下角浮動按鈕
- **圖標**: Icons.calendar_month ↔ Icons.timeline
- **背景**: AppColors.primary
- **前景**: Colors.white

### 4. Balance Tab (費用計算頁面)
**組件**: `BalancePage`

#### Layout結構
```
┌─────────────────────────────────────┐
│ BlocProvider<BalanceBloc>           │
│ ┌─────────────────────────────────┐ │
│ │ BalancePage Content             │ │
│ │ (具體實現待開發)                │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 5. TODOs Tab (待辦事項頁面)
**組件**: `TodoKanbanWidget`

#### Layout結構
```
┌─────────────────────────────────────┐
│ TodoKanbanWidget                    │
│ ┌─────────────────────────────────┐ │
│ │ Kanban Board Layout             │ │
│ │ ┌─────┬─────┬─────┬─────────────┐ │ │
│ │ │ To  │ In  │Done │ Archived    │ │ │
│ │ │ Do  │Prog │     │             │ │ │
│ │ ├─────┼─────┼─────┼─────────────┤ │ │
│ │ │ □   │ ⏳  │ ✅  │ 📦          │ │ │
│ │ │Task1│Task2│Task3│ Task4       │ │ │
│ │ │ □   │     │ ✅  │             │ │ │
│ │ │Task5│     │Task6│             │ │ │
│ │ └─────┴─────┴─────┴─────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### Kanban功能
- **四列布局**: To Do, In Progress, Done, Archived
- **拖拽功能**: 任務卡片可拖拽移動
- **任務卡片**: 顯示任務標題、狀態、優先級
- **添加任務**: 支持快速添加新任務

### 6. Map Tab (地圖頁面)
**組件**: 地圖功能組件

#### Layout結構
```
┌─────────────────────────────────────┐
│ Center                              │
│ ┌─────────────────────────────────┐ │
│ │ Column (MainAxisAlignment.center)│ │
│ │ ┌─────┐                         │ │
│ │ │ 🗺️  │ Map Feature             │ │
│ │ └─────┘ Coming Soon             │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 7. ToGo Tab (地點頁面)
**組件**: 地點管理組件

#### Layout結構
```
┌─────────────────────────────────────┐
│ Center                              │
│ ┌─────────────────────────────────┐ │
│ │ Column (MainAxisAlignment.center)│ │
│ │ ┌─────┐                         │ │
│ │ │ 📍  │ Places to Go            │ │
│ │ └─────┘ Feature Coming Soon     │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 8. Tags Tab (標籤頁面)
**組件**: 標籤管理組件

#### Layout結構
```
┌─────────────────────────────────────┐
│ Center                              │
│ ┌─────────────────────────────────┐ │
│ │ Column (MainAxisAlignment.center)│ │
│ │ ┌─────┐                         │ │
│ │ │ 🏷️  │ Tag Management          │ │
│ │ └─────┘ Feature Coming Soon     │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### Tab配置系統
- **響應式Tab數量**: 移動端4個，iPad橫屏5個
- **Tab寬度**: 動態計算 (constraints.maxWidth / tabCount)
- **徽章支持**: Chat Tab顯示未讀消息數量
- **圖標大小**: 20px統一尺寸

---

## 🧩 核心組件

### 1. SpaceCard (空間卡片)
**文件位置**: `frontend/lib/features/spaces/presentation/widgets/space_card.dart`

#### 視覺設計
- **容器樣式**: 圓角卡片，邊框，陰影
- **圖標設計**: 40x40px圓角容器，分類圖標
- **文字層次**: 空間名稱 + 分類描述
- **狀態指示**: 未讀消息數量、邀請狀態

#### 交互效果
- **點擊反饋**: InkWell水波紋效果
- **邀請操作**: 接受/拒絕邀請按鈕
- **響應式**: 自適應卡片圓角和陰影

### 2. SpaceHeader (空間頭部)
**文件位置**: `frontend/lib/features/spaces/presentation/widgets/space_header.dart`

#### 組件結構
- **空間圖標**: 48x48px圓角容器
- **空間信息**: 名稱 + 分類 + 成員數
- **在線狀態**: OnlineMembersWidget

### 3. SpaceEnhancedCalendar (增強日曆)
**文件位置**: `frontend/lib/features/spaces/presentation/widgets/space_enhanced_calendar.dart`

#### 日曆特性
- **TableCalendar**: 基於table_calendar包
- **自定義樣式**: 與空間主題色匹配
- **動畫效果**: 淡入淡出 + 縮放動畫
- **事件標記**: 支持事件數量顯示

#### 日期單元格
- **今日標記**: 邊框高亮
- **選中狀態**: 背景色 + 陰影
- **事件指示**: 右上角數量標記

### 4. SpaceResponsiveTimeline (響應式時間軸)
**文件位置**: `frontend/lib/features/spaces/presentation/widgets/space_responsive_timeline.dart`

#### 布局結構
- **左側**: 日期選擇器（80px寬度）
- **右側**: 內容展示區域
- **無限滾動**: 支持大範圍日期導航

#### 日期選擇器
- **垂直列表**: 50px項目高度
- **性能優化**: itemExtent + cacheExtent
- **視覺反饋**: 選中日期高亮

### 5. ColorPickerWidget (顏色選擇器)
**文件位置**: `frontend/lib/features/spaces/presentation/widgets/color_picker_widget.dart`

#### 顏色網格
- **8列布局**: 固定交叉軸數量
- **預設顏色**: 24種預定義顏色
- **選中狀態**: 邊框 + 對勾圖標
- **響應式**: 自適應間距

---

## 🎨 樣式系統

### 1. SpaceThemeProvider (空間主題提供者)
**文件位置**: `frontend/lib/features/spaces/presentation/widgets/space_theme_provider.dart`

#### 主題顏色
- **primaryColor**: 空間主色調
- **accentColor**: 主色調10%透明度
- **backgroundColor**: 主色調5%透明度
- **surfaceColor**: 主色調8%透明度

#### 輔助方法
- **lightBackground**: 3%透明度背景
- **mediumAccent**: 15%透明度強調色
- **darkAccent**: 80%透明度深色強調
- **createThemedButtonStyle**: 主題按鈕樣式

### 2. SpaceColors (空間顏色)
**文件位置**: `frontend/lib/features/spaces/data/models/space_colors.dart`

#### 預定義顏色
```dart
// 基礎顏色 (8種)
Blue, Green, Orange, Pink, Purple, Indigo, Teal, Red

// 擴展顏色 (16種)
Yellow, Brown, Blue Grey, Light Green, Deep Orange, 
Deep Purple, Cyan, Lime, 等...
```

#### 分類默認顏色
- **General**: Blue (#2196F3)
- **Family**: Green (#4CAF50)
- **Friends**: Orange (#FF9800)
- **Couple**: Pink (#E91E63)
- **Team**: Purple (#9C27B0)
- **Work**: Indigo (#3F51B5)
- **Hobby**: Teal (#009688)
- **Travel**: Yellow (#FFEB3B)
- **Study**: Deep Purple (#673AB7)
- **Project**: Cyan (#00BCD4)

### 3. SpaceCategoryHelper (分類助手)
**文件位置**: `frontend/lib/features/spaces/data/models/space_category_helper.dart`

#### 分類信息
- **顯示名稱**: 英文分類名稱
- **描述文字**: 分類用途說明
- **圖標映射**: Material Icons圖標
- **顏色映射**: 默認顏色配置

#### 核心方法
- **getEffectiveColor**: 獲取有效顏色（自定義或默認）
- **getDisplayName**: 獲取顯示名稱
- **getDescription**: 獲取描述文字
- **getIcon**: 獲取分類圖標

---

## 📐 布局組件

### 1. ResponsiveUtils (響應式工具)
**文件位置**: `frontend/lib/core/utils/responsive_utils.dart`

#### 斷點定義
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px - 1440px
- **Large Desktop**: > 1440px

#### 響應式方法
- **getResponsiveCardRadius**: 自適應卡片圓角
- **getResponsiveElevation**: 自適應陰影高度
- **getResponsiveFontSize**: 自適應字體大小
- **getResponsiveSpacing**: 自適應間距

### 2. ResponsiveBuilder (響應式構建器)
**文件位置**: `frontend/lib/core/utils/responsive_utils.dart`

#### 使用方式
```dart
ResponsiveBuilder(
  builder: (context, deviceType) {
    // 項目規則：所有設備都使用移動端實現
    return _buildMobileLayout(context);
  },
)
```

---

## 🎬 動畫效果

### 1. AnimationManager (動畫管理器)
**文件位置**: `frontend/lib/core/animations/animation_manager.dart`

#### 動畫時長
- **fastDuration**: 200ms
- **normalDuration**: 300ms
- **slowDuration**: 500ms

#### 動畫曲線
- **fastCurve**: Curves.easeOut
- **normalCurve**: Curves.easeInOut
- **listItemCurve**: Curves.easeOutCubic

### 2. 微交互動畫
**文件位置**: `frontend/lib/core/animations/micro_interactions.dart`

#### 組件類型
- **ButtonPressAnimation**: 按鈕按壓效果
- **HapticButton**: 觸覺反饋按鈕
- **StaggeredListAnimation**: 交錯列表動畫
- **InteractiveListItem**: 交互式列表項

#### 動畫效果
- **縮放動畫**: 按壓時0.95倍縮放
- **淡入動畫**: 透明度0到1
- **滑入動畫**: 從下方滑入
- **交錯延遲**: 50ms間隔

---

## 📱 響應式設計

### 設計原則
根據項目規則，所有設備類型都使用移動端實現，確保統一的用戶體驗：

#### 統一標準
- **移動端優先**: 以移動端為設計標準
- **單一實現**: 避免為不同設備創建不同實現
- **一致體驗**: 所有設備保持相同的布局和交互

#### 適配策略
- **彈性布局**: 使用Flex、Expanded等彈性組件
- **相對尺寸**: 基於屏幕比例的尺寸計算
- **觸摸優化**: 適合觸摸操作的按鈕和間距

### 性能優化
- **列表虛擬化**: ListView.builder + itemExtent
- **圖片緩存**: 頭像和圖標緩存
- **動畫優化**: RepaintBoundary邊界控制
- **內存管理**: AutomaticKeepAliveClientMixin

---

## 🔧 開發工具

### 調試功能
- **可拖動調試按鈕**: SpaceDetailPage中的調試工具
- **性能監控**: 動畫性能和渲染邊界
- **狀態檢查**: Bloc狀態調試

### 代碼規範
- **英文命名**: 所有代碼使用英文
- **英文UI**: 所有用戶界面文字使用英文
- **模塊化**: 功能分離，組件復用
- **類型安全**: 嚴格的Dart類型檢查

---

## 📊 組件使用統計

### 頁面組件分佈
| 頁面 | 主要組件數量 | 複雜度 | 維護優先級 |
|------|-------------|--------|-----------|
| SpaceDetailPage | 15+ | 高 | 🔴 高 |
| CreateSpacePage | 8+ | 中 | 🟡 中 |
| SpaceSettingsPage | 12+ | 中 | 🟡 中 |
| SpaceInvitesPage | 6+ | 低 | 🟢 低 |
| SpaceMembersPage | 4+ | 低 | 🟢 低 |

### 核心組件重用性
| 組件 | 使用頻率 | 重用性 | 優化建議 |
|------|---------|--------|----------|
| SpaceCard | 高 | 高 | ✅ 已優化 |
| SpaceHeader | 中 | 中 | 🔄 可優化 |
| ColorPickerWidget | 低 | 高 | ✅ 已優化 |
| SpaceThemeProvider | 高 | 高 | ✅ 已優化 |

---

## 🎯 UI/UX 設計指南

### 視覺層次
1. **主要內容**: 空間名稱、核心功能按鈕
2. **次要內容**: 分類信息、成員數量
3. **輔助內容**: 時間戳、狀態指示器

### 顏色使用原則
- **主色調**: 空間分類或自定義顏色
- **中性色**: 文字、邊框、背景
- **狀態色**: 成功、警告、錯誤提示
- **透明度**: 10%背景、5%淺背景、80%深色強調

### 間距系統
- **基礎間距**: 8px的倍數
- **組件間距**: 16px (默認)
- **頁面邊距**: 16px (移動端)
- **卡片內邊距**: 16px水平，12px垂直

### 字體層次
- **標題**: 18-24px，FontWeight.w600
- **正文**: 14-16px，FontWeight.normal
- **輔助文字**: 12-14px，FontWeight.w500
- **標籤**: 10-12px，FontWeight.w500

---

## 🔄 狀態管理

### Bloc模式使用
- **SpacesBloc**: 空間列表管理
- **SpaceDetailBloc**: 空間詳情管理
- **TimelineBloc**: 時間軸內容管理

### 狀態類型
- **Loading**: 加載中狀態
- **Loaded**: 數據加載完成
- **Error**: 錯誤狀態
- **Empty**: 空數據狀態

---

## 🚀 性能優化建議

### 渲染優化
1. **RepaintBoundary**: 為動畫組件添加重繪邊界
2. **const構造函數**: 靜態組件使用const
3. **ListView.builder**: 大列表使用構建器模式
4. **itemExtent**: 固定項目高度提升性能

### 內存優化
1. **AutomaticKeepAliveClientMixin**: 保持重要頁面狀態
2. **cacheExtent**: 合理設置緩存範圍
3. **addAutomaticKeepAlives**: 根據需要禁用自動保持

### 動畫優化
1. **AnimationController**: 及時dispose動畫控制器
2. **Curves**: 使用合適的動畫曲線
3. **Duration**: 避免過長的動畫時間

---

## 📋 開發檢查清單

### 新組件開發
- [ ] 遵循命名規範（英文）
- [ ] 實現響應式設計
- [ ] 添加適當的動畫效果
- [ ] 支持主題色彩系統
- [ ] 處理空狀態和錯誤狀態
- [ ] 添加觸覺反饋
- [ ] 優化性能表現

### UI更新檢查
- [ ] 保持設計一致性
- [ ] 測試不同屏幕尺寸
- [ ] 驗證顏色對比度
- [ ] 檢查動畫流暢性
- [ ] 確保觸摸目標大小
- [ ] 測試深色模式兼容性

### 代碼質量
- [ ] 組件可復用性
- [ ] 代碼可讀性
- [ ] 性能優化
- [ ] 錯誤處理
- [ ] 文檔完整性

---

## 🔗 相關文件索引

### 核心文件路徑
```
frontend/lib/features/spaces/
├── presentation/
│   ├── pages/
│   │   ├── space_detail_page.dart
│   │   ├── create_space_page.dart
│   │   ├── space_settings_page.dart
│   │   ├── space_invites_page.dart
│   │   └── space_members_page.dart
│   └── widgets/
│       ├── space_card.dart
│       ├── space_header.dart
│       ├── space_enhanced_calendar.dart
│       ├── space_responsive_timeline.dart
│       ├── space_theme_provider.dart
│       └── color_picker_widget.dart
├── data/
│   └── models/
│       ├── space_models.dart
│       ├── space_colors.dart
│       └── space_category_helper.dart
└── domain/
    └── usecases/
        └── clear_space_data_usecase.dart
```

### 共享組件路徑
```
frontend/lib/core/
├── theme/
│   ├── app_colors.dart
│   └── app_theme.dart
├── utils/
│   └── responsive_utils.dart
├── animations/
│   ├── animation_manager.dart
│   └── micro_interactions.dart
└── constants/
    └── app_constants.dart
```

---

*最後更新: 2025-01-30*
*文檔版本: v1.0*
